mod automation;
mod entity;
mod event_bus;
mod values_objects;

use std::sync::Arc;

use entity::{<PERSON><PERSON><PERSON>, EntityManager, components::OnOffComponent};
use values_objects::Id;

use crate::{
    automation::AutomationEngine, entity::{
        components::{
            BrightnessComponent, ButtonLongPressComponent, ButtonPressComponent, ColorComponent,
        }, EntityState
    }, event_bus::EventBus, values_objects::Color
};

#[tokio::main]
async fn main() {
    let event_bus = Arc::new(EventBus::new());
    let mut entity_manager = EntityManager::new(event_bus.clone());
    let mut automation_engine = AutomationEngine::new(event_bus.clone());

    let mut light = Entity::new(
        Id::new("light_1".to_string()),
        "Light".to_string(),
        "A light".to_string(),
    );
    light
        .add_component(Box::new(OnOffComponent { is_on: true }))
        .add_component(Box::new(BrightnessComponent { brightness: 50 }))
        .add_component(Box::new(ColorComponent {
            color: Color::new(255, 0, 0),
        }));

    let mut button = Entity::new(
        Id::new("button_1".to_string()),
        "Button".to_string(),
        "A button".to_string(),
    );

    button
        .add_component(Box::new(ButtonPressComponent {
            button_id: "button_1".to_string(),
        }))
        .add_component(Box::new(ButtonLongPressComponent {
            button_id: "button_1".to_string(),
        }))
        .add_component(Box::new(ButtonPressComponent {
            button_id: "button_2".to_string(),
        }))
        .add_component(Box::new(ButtonLongPressComponent {
            button_id: "button_2".to_string(),
        }));

    entity_manager.register_entity(light);
    entity_manager.register_entity(button);

    let light_entities = entity_manager.get_entities_with_component::<OnOffComponent>();
    println!("Light entities: {:?}", light_entities);

    let button_entities = entity_manager.get_entities_with_component::<ButtonPressComponent>();
    println!("Button entities: {:?}", button_entities);
    
    println!();
    println!();

    automation_engine.start().await;

    let _ = entity_manager
        .update_entity_state(
            Id::new("light_1".to_string()),
            EntityState::from_components(vec![Box::new(OnOffComponent { is_on: false })]),
        )
        .await;

    println!();
    println!();
    
    let l = entity_manager.get_by_id(Id::new("light_1".to_string()));
    println!("Light: {:?}", l);
}
