use std::time::Duration;
use crate::values_objects::{Id, Color};

/// Result of executing a workflow node
#[derive(Debug, Clone, PartialEq)]
pub enum NodeExecutionResult {
    Success,
    Failure,
    Pending,
    Skipped,
}

/// Base trait for all workflow nodes that can be executed
pub trait WorkflowNode: std::fmt::Debug {
    fn execute(&self) -> NodeExecutionResult;
    fn get_name(&self) -> &str;
    fn get_description(&self) -> &str;
}

/// Trigger nodes that start automation workflows
#[derive(Debug, Clone)]
pub enum TriggerNodeType {
    /// Triggered when an entity state changes
    EntityStateChange {
        entity_id: Id,
        from_state: Option<String>,
        to_state: Option<String>,
    },
    /// Triggered at specific time intervals
    TimeInterval {
        interval: Duration,
    },
    /// Triggered at specific times
    ScheduledTime {
        hour: u8,
        minute: u8,
        days_of_week: Vec<u8>, // 0 = Sunday, 1 = Monday, etc.
    },
    /// Triggered by manual activation
    Manual {
        trigger_name: String,
    },
    /// Triggered when a numeric value crosses a threshold
    NumericThreshold {
        entity_id: Id,
        threshold: f64,
        comparison: ThresholdComparison,
    },
    /// Triggered by device events (button press, etc.)
    DeviceEvent {
        device_id: Id,
        event_type: String,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum ThresholdComparison {
    GreaterThan,
    LessThan,
    Equal,
    GreaterThanOrEqual,
    LessThanOrEqual,
}

#[derive(Debug)]
pub struct TriggerNode {
    pub id: Id,
    pub name: String,
    pub description: String,
    pub trigger_type: TriggerNodeType,
    pub enabled: bool,
}

impl TriggerNode {
    pub fn new(id: Id, name: String, description: String, trigger_type: TriggerNodeType) -> Self {
        Self {
            id,
            name,
            description,
            trigger_type,
            enabled: true,
        }
    }
}

impl WorkflowNode for TriggerNode {
    fn execute(&self) -> NodeExecutionResult {
        if !self.enabled {
            return NodeExecutionResult::Skipped;
        }
        
        // In a real implementation, this would check if the trigger condition is met
        // For now, we'll return Success as a placeholder
        NodeExecutionResult::Success
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    fn get_description(&self) -> &str {
        &self.description
    }
}

/// Condition nodes that evaluate boolean expressions
#[derive(Debug, Clone)]
pub enum ConditionNodeType {
    /// Check entity state
    EntityState {
        entity_id: Id,
        expected_state: String,
    },
    /// Check numeric value comparison
    NumericComparison {
        entity_id: Id,
        value: f64,
        comparison: ThresholdComparison,
    },
    /// Check time-based conditions
    TimeCondition {
        start_hour: u8,
        start_minute: u8,
        end_hour: u8,
        end_minute: u8,
    },
    /// Check day of week
    DayOfWeek {
        days: Vec<u8>, // 0 = Sunday, 1 = Monday, etc.
    },
    /// Logical AND of multiple conditions
    And {
        conditions: Vec<Id>, // References to other condition nodes
    },
    /// Logical OR of multiple conditions
    Or {
        conditions: Vec<Id>, // References to other condition nodes
    },
    /// Logical NOT of a condition
    Not {
        condition: Id, // Reference to another condition node
    },
    /// Custom script condition
    CustomScript {
        script: String,
    },
}

#[derive(Debug)]
pub struct ConditionNode {
    pub id: Id,
    pub name: String,
    pub description: String,
    pub condition_type: ConditionNodeType,
    pub enabled: bool,
}

impl ConditionNode {
    pub fn new(id: Id, name: String, description: String, condition_type: ConditionNodeType) -> Self {
        Self {
            id,
            name,
            description,
            condition_type,
            enabled: true,
        }
    }
}

impl WorkflowNode for ConditionNode {
    fn execute(&self) -> NodeExecutionResult {
        if !self.enabled {
            return NodeExecutionResult::Skipped;
        }
        
        // In a real implementation, this would evaluate the condition
        // For now, we'll return Success as a placeholder
        NodeExecutionResult::Success
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    fn get_description(&self) -> &str {
        &self.description
    }
}

/// Action nodes that perform operations
#[derive(Debug, Clone)]
pub enum ActionNodeType {
    /// Turn entity on/off
    EntityOnOff {
        entity_id: Id,
        turn_on: bool,
    },
    /// Set entity brightness
    SetBrightness {
        entity_id: Id,
        brightness: u8, // 0-100
    },
    /// Set entity color
    SetColor {
        entity_id: Id,
        color: Color,
    },
    /// Send notification
    SendNotification {
        title: String,
        message: String,
        priority: NotificationPriority,
    },
    /// Wait/delay
    Delay {
        duration: Duration,
    },
    /// Call service
    CallService {
        service_name: String,
        parameters: Vec<(String, String)>,
    },
    /// Execute script
    ExecuteScript {
        script: String,
    },
    /// Trigger another automation
    TriggerAutomation {
        automation_id: Id,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum NotificationPriority {
    Low,
    Normal,
    High,
    Critical,
}

#[derive(Debug)]
pub struct ActionNode {
    pub id: Id,
    pub name: String,
    pub description: String,
    pub action_type: ActionNodeType,
    pub enabled: bool,
}

impl ActionNode {
    pub fn new(id: Id, name: String, description: String, action_type: ActionNodeType) -> Self {
        Self {
            id,
            name,
            description,
            action_type,
            enabled: true,
        }
    }
}

impl WorkflowNode for ActionNode {
    fn execute(&self) -> NodeExecutionResult {
        if !self.enabled {
            return NodeExecutionResult::Skipped;
        }
        
        // In a real implementation, this would perform the action
        // For now, we'll return Success as a placeholder
        NodeExecutionResult::Success
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    fn get_description(&self) -> &str {
        &self.description
    }
}

/// Logic nodes for complex flow control
#[derive(Debug, Clone)]
pub enum LogicNodeType {
    /// Split execution into multiple parallel paths
    Parallel {
        branch_names: Vec<String>,
    },
    /// Choose one path based on condition result
    Switch {
        condition_results: Vec<(NodeExecutionResult, Id)>, // (condition result, next node)
        default_next: Option<Id>,
    },
    /// Merge multiple paths back together
    Merge {
        wait_for_all: bool, // If true, wait for all inputs; if false, proceed on first
    },
    /// Loop with condition
    Loop {
        condition: Id, // Reference to condition node
        max_iterations: Option<u32>,
    },
}

#[derive(Debug)]
pub struct LogicNode {
    pub id: Id,
    pub name: String,
    pub description: String,
    pub logic_type: LogicNodeType,
    pub enabled: bool,
}

impl LogicNode {
    pub fn new(id: Id, name: String, description: String, logic_type: LogicNodeType) -> Self {
        Self {
            id,
            name,
            description,
            logic_type,
            enabled: true,
        }
    }
}

impl WorkflowNode for LogicNode {
    fn execute(&self) -> NodeExecutionResult {
        if !self.enabled {
            return NodeExecutionResult::Skipped;
        }
        
        // In a real implementation, this would handle flow control logic
        // For now, we'll return Success as a placeholder
        NodeExecutionResult::Success
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    fn get_description(&self) -> &str {
        &self.description
    }
}

/// Unified workflow node that can contain any type of node
#[derive(Debug)]
pub enum WorkflowNodeData {
    Trigger(TriggerNode),
    Condition(ConditionNode),
    Action(ActionNode),
    Logic(LogicNode),
}

impl WorkflowNodeData {
    pub fn get_id(&self) -> &Id {
        match self {
            WorkflowNodeData::Trigger(node) => &node.id,
            WorkflowNodeData::Condition(node) => &node.id,
            WorkflowNodeData::Action(node) => &node.id,
            WorkflowNodeData::Logic(node) => &node.id,
        }
    }

    pub fn is_enabled(&self) -> bool {
        match self {
            WorkflowNodeData::Trigger(node) => node.enabled,
            WorkflowNodeData::Condition(node) => node.enabled,
            WorkflowNodeData::Action(node) => node.enabled,
            WorkflowNodeData::Logic(node) => node.enabled,
        }
    }

    pub fn set_enabled(&mut self, enabled: bool) {
        match self {
            WorkflowNodeData::Trigger(node) => node.enabled = enabled,
            WorkflowNodeData::Condition(node) => node.enabled = enabled,
            WorkflowNodeData::Action(node) => node.enabled = enabled,
            WorkflowNodeData::Logic(node) => node.enabled = enabled,
        }
    }
}

impl WorkflowNode for WorkflowNodeData {
    fn execute(&self) -> NodeExecutionResult {
        match self {
            WorkflowNodeData::Trigger(node) => node.execute(),
            WorkflowNodeData::Condition(node) => node.execute(),
            WorkflowNodeData::Action(node) => node.execute(),
            WorkflowNodeData::Logic(node) => node.execute(),
        }
    }

    fn get_name(&self) -> &str {
        match self {
            WorkflowNodeData::Trigger(node) => node.get_name(),
            WorkflowNodeData::Condition(node) => node.get_name(),
            WorkflowNodeData::Action(node) => node.get_name(),
            WorkflowNodeData::Logic(node) => node.get_name(),
        }
    }

    fn get_description(&self) -> &str {
        match self {
            WorkflowNodeData::Trigger(node) => node.get_description(),
            WorkflowNodeData::Condition(node) => node.get_description(),
            WorkflowNodeData::Action(node) => node.get_description(),
            WorkflowNodeData::Logic(node) => node.get_description(),
        }
    }
} 