use std::collections::{HashMap, HashSet, VecDeque};
use std::fmt::Debug;
use crate::values_objects::Id;

pub type NodeId = Id;

#[derive(Debug, <PERSON>lone)]
pub struct DAGNode<T> {
    pub id: NodeId,
    pub data: T,
    pub incoming_edges: HashSet<NodeId>,
    pub outgoing_edges: HashSet<NodeId>,
}

impl<T> DAGNode<T> {
    pub fn new(id: NodeId, data: T) -> Self {
        Self {
            id,
            data,
            incoming_edges: HashSet::new(),
            outgoing_edges: HashSet::new(),
        }
    }
}

#[derive(Debug)]
pub struct DAG<T> {
    nodes: HashMap<NodeId, DAGNode<T>>,
    root_nodes: HashSet<NodeId>,
}

#[derive(Debug, PartialEq)]
pub enum DAGError {
    NodeNotFound(NodeId),
    CycleDetected,
    NodeAlreadyExists(NodeId),
    SelfEdgeNotAllowed,
}

impl<T> DAG<T> {
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            root_nodes: HashSet::new(),
        }
    }

    /// Add a node to the DAG
    pub fn add_node(&mut self, node: DAGNode<T>) -> Result<(), DAGError> {
        if self.nodes.contains_key(&node.id) {
            return Err(DAGError::NodeAlreadyExists(node.id.clone()));
        }

        let node_id = node.id.clone();
        self.nodes.insert(node_id.clone(), node);
        
        // If this node has no incoming edges, it's a root node
        if self.nodes[&node_id].incoming_edges.is_empty() {
            self.root_nodes.insert(node_id);
        }

        Ok(())
    }

    /// Add an edge between two nodes
    pub fn add_edge(&mut self, from: &NodeId, to: &NodeId) -> Result<(), DAGError> {
        if from == to {
            return Err(DAGError::SelfEdgeNotAllowed);
        }

        if !self.nodes.contains_key(from) {
            return Err(DAGError::NodeNotFound(from.clone()));
        }

        if !self.nodes.contains_key(to) {
            return Err(DAGError::NodeNotFound(to.clone()));
        }

        // Check if adding this edge would create a cycle
        if self.would_create_cycle(from, to)? {
            return Err(DAGError::CycleDetected);
        }

        // Add the edge
        self.nodes.get_mut(from).unwrap().outgoing_edges.insert(to.clone());
        self.nodes.get_mut(to).unwrap().incoming_edges.insert(from.clone());

        // Update root nodes
        self.root_nodes.remove(to);

        Ok(())
    }

    /// Remove a node from the DAG
    pub fn remove_node(&mut self, node_id: &NodeId) -> Result<DAGNode<T>, DAGError> {
        let node = self.nodes.remove(node_id)
            .ok_or_else(|| DAGError::NodeNotFound(node_id.clone()))?;

        // Remove all edges to and from this node
        for incoming_id in &node.incoming_edges {
            if let Some(incoming_node) = self.nodes.get_mut(incoming_id) {
                incoming_node.outgoing_edges.remove(node_id);
            }
        }

        for outgoing_id in &node.outgoing_edges {
            if let Some(outgoing_node) = self.nodes.get_mut(outgoing_id) {
                outgoing_node.incoming_edges.remove(node_id);
                // If this node now has no incoming edges, it becomes a root
                if outgoing_node.incoming_edges.is_empty() {
                    self.root_nodes.insert(outgoing_id.clone());
                }
            }
        }

        self.root_nodes.remove(node_id);

        Ok(node)
    }

    /// Get a node by ID
    pub fn get_node(&self, node_id: &NodeId) -> Option<&DAGNode<T>> {
        self.nodes.get(node_id)
    }

    /// Get a mutable reference to a node by ID
    pub fn get_node_mut(&mut self, node_id: &NodeId) -> Option<&mut DAGNode<T>> {
        self.nodes.get_mut(node_id)
    }

    /// Get all root nodes (nodes with no incoming edges)
    pub fn get_root_nodes(&self) -> &HashSet<NodeId> {
        &self.root_nodes
    }

    /// Get children of a node
    pub fn get_children(&self, node_id: &NodeId) -> Option<&HashSet<NodeId>> {
        self.nodes.get(node_id).map(|node| &node.outgoing_edges)
    }

    /// Get parents of a node
    pub fn get_parents(&self, node_id: &NodeId) -> Option<&HashSet<NodeId>> {
        self.nodes.get(node_id).map(|node| &node.incoming_edges)
    }

    /// Perform topological sort of the DAG
    pub fn topological_sort(&self) -> Vec<NodeId> {
        let mut result = Vec::new();
        let mut in_degree: HashMap<NodeId, usize> = HashMap::new();
        let mut queue = VecDeque::new();

        // Initialize in-degree count for all nodes
        for (node_id, node) in &self.nodes {
            in_degree.insert(node_id.clone(), node.incoming_edges.len());
            if node.incoming_edges.is_empty() {
                queue.push_back(node_id.clone());
            }
        }

        // Process nodes with no incoming edges
        while let Some(node_id) = queue.pop_front() {
            result.push(node_id.clone());

            if let Some(node) = self.nodes.get(&node_id) {
                for neighbor in &node.outgoing_edges {
                    if let Some(degree) = in_degree.get_mut(neighbor) {
                        *degree -= 1;
                        if *degree == 0 {
                            queue.push_back(neighbor.clone());
                        }
                    }
                }
            }
        }

        result
    }

    /// Perform depth-first traversal starting from root nodes
    pub fn dfs_traversal(&self) -> Vec<NodeId> {
        let mut visited = HashSet::new();
        let mut result = Vec::new();

        for root_id in &self.root_nodes {
            self.dfs_visit(root_id, &mut visited, &mut result);
        }

        result
    }

    /// Perform breadth-first traversal starting from root nodes
    pub fn bfs_traversal(&self) -> Vec<NodeId> {
        let mut visited = HashSet::new();
        let mut result = Vec::new();
        let mut queue = VecDeque::new();

        // Start with all root nodes
        for root_id in &self.root_nodes {
            queue.push_back(root_id.clone());
            visited.insert(root_id.clone());
        }

        while let Some(node_id) = queue.pop_front() {
            result.push(node_id.clone());

            if let Some(node) = self.nodes.get(&node_id) {
                for child_id in &node.outgoing_edges {
                    if !visited.contains(child_id) {
                        visited.insert(child_id.clone());
                        queue.push_back(child_id.clone());
                    }
                }
            }
        }

        result
    }

    /// Check if adding an edge would create a cycle
    fn would_create_cycle(&self, from: &NodeId, to: &NodeId) -> Result<bool, DAGError> {
        // If there's already a path from 'to' to 'from', adding edge from->to would create a cycle
        Ok(self.has_path(to, from))
    }

    /// Check if there's a path between two nodes
    fn has_path(&self, from: &NodeId, to: &NodeId) -> bool {
        let mut visited = HashSet::new();
        self.has_path_recursive(from, to, &mut visited)
    }

    /// Recursive helper for path checking
    fn has_path_recursive(&self, current: &NodeId, target: &NodeId, visited: &mut HashSet<NodeId>) -> bool {
        if current == target {
            return true;
        }

        if visited.contains(current) {
            return false;
        }

        visited.insert(current.clone());

        if let Some(node) = self.nodes.get(current) {
            for child in &node.outgoing_edges {
                if self.has_path_recursive(child, target, visited) {
                    return true;
                }
            }
        }

        false
    }

    /// Recursive helper for DFS traversal
    fn dfs_visit(&self, node_id: &NodeId, visited: &mut HashSet<NodeId>, result: &mut Vec<NodeId>) {
        if visited.contains(node_id) {
            return;
        }

        visited.insert(node_id.clone());
        result.push(node_id.clone());

        if let Some(node) = self.nodes.get(node_id) {
            for child_id in &node.outgoing_edges {
                self.dfs_visit(child_id, visited, result);
            }
        }
    }

    /// Get number of nodes in the DAG
    pub fn node_count(&self) -> usize {
        self.nodes.len()
    }

    /// Get number of edges in the DAG
    pub fn edge_count(&self) -> usize {
        self.nodes.values().map(|node| node.outgoing_edges.len()).sum()
    }

    /// Check if the DAG is empty
    pub fn is_empty(&self) -> bool {
        self.nodes.is_empty()
    }

    /// Get all node IDs
    pub fn get_all_node_ids(&self) -> Vec<NodeId> {
        self.nodes.keys().cloned().collect()
    }
}

impl<T> Default for DAG<T> {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_add_node() {
        let mut dag = DAG::new();
        let node = DAGNode::new(Id::new("node1".to_string()), "data1");
        
        assert!(dag.add_node(node).is_ok());
        assert_eq!(dag.node_count(), 1);
    }

    #[test]
    fn test_add_edge() {
        let mut dag = DAG::new();
        let node1 = DAGNode::new(Id::new("node1".to_string()), "data1");
        let node2 = DAGNode::new(Id::new("node2".to_string()), "data2");
        
        let id1 = node1.id.clone();
        let id2 = node2.id.clone();
        
        dag.add_node(node1).unwrap();
        dag.add_node(node2).unwrap();
        
        assert!(dag.add_edge(&id1, &id2).is_ok());
        assert_eq!(dag.edge_count(), 1);
    }

    #[test]
    fn test_cycle_detection() {
        let mut dag = DAG::new();
        let node1 = DAGNode::new(Id::new("node1".to_string()), "data1");
        let node2 = DAGNode::new(Id::new("node2".to_string()), "data2");
        
        let id1 = node1.id.clone();
        let id2 = node2.id.clone();
        
        dag.add_node(node1).unwrap();
        dag.add_node(node2).unwrap();
        
        dag.add_edge(&id1, &id2).unwrap();
        
        // This should fail because it would create a cycle
        assert_eq!(dag.add_edge(&id2, &id1), Err(DAGError::CycleDetected));
    }

    #[test]
    fn test_topological_sort() {
        let mut dag = DAG::new();
        let node1 = DAGNode::new(Id::new("node1".to_string()), "data1");
        let node2 = DAGNode::new(Id::new("node2".to_string()), "data2");
        let node3 = DAGNode::new(Id::new("node3".to_string()), "data3");
        
        let id1 = node1.id.clone();
        let id2 = node2.id.clone();
        let id3 = node3.id.clone();
        
        dag.add_node(node1).unwrap();
        dag.add_node(node2).unwrap();
        dag.add_node(node3).unwrap();
        
        dag.add_edge(&id1, &id2).unwrap();
        dag.add_edge(&id2, &id3).unwrap();
        
        let sorted = dag.topological_sort();
        assert_eq!(sorted.len(), 3);
        
        // node1 should come before node2, node2 should come before node3
        let pos1 = sorted.iter().position(|x| x == &id1).unwrap();
        let pos2 = sorted.iter().position(|x| x == &id2).unwrap();
        let pos3 = sorted.iter().position(|x| x == &id3).unwrap();
        
        assert!(pos1 < pos2);
        assert!(pos2 < pos3);
    }
} 