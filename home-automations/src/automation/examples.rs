use crate::automation::{
    Automation, TriggerNode, TriggerNodeType, ConditionNode, ConditionNodeType,
    ActionNode, ActionNodeType, LogicNode, LogicNodeType,
    WorkflowNodeData, ThresholdComparison, NotificationPriority
};
use crate::values_objects::{Id, Color};
use std::time::Duration;

/// Example: Simple automation - turn on light when button is pressed
pub fn create_simple_button_automation() -> Result<Automation, Box<dyn std::error::Error>> {
    let mut automation = Automation::new(
        Id::new("simple_button_automation".to_string()),
        "Simple Button Light Control".to_string(),
        "Turn on living room light when button is pressed".to_string(),
    );

    // Create trigger node - button press
    let trigger = TriggerNode::new(
        Id::new("button_trigger".to_string()),
        "Button Press Trigger".to_string(),
        "Triggered when living room button is pressed".to_string(),
        TriggerNodeType::DeviceEvent {
            device_id: Id::new("living_room_button".to_string()),
            event_type: "press".to_string(),
        },
    );

    // Create action node - turn on light
    let action = ActionNode::new(
        Id::new("light_action".to_string()),
        "Turn On Light".to_string(),
        "Turn on the living room light".to_string(),
        ActionNodeType::EntityOnOff {
            entity_id: Id::new("living_room_light".to_string()),
            turn_on: true,
        },
    );

    // Add nodes to automation
    automation.add_workflow_node(WorkflowNodeData::Trigger(trigger))?;
    automation.add_workflow_node(WorkflowNodeData::Action(action))?;

    // Connect trigger to action
    automation.connect_nodes(
        &Id::new("button_trigger".to_string()),
        &Id::new("light_action".to_string()),
    )?;

    Ok(automation)
}

/// Example: Complex automation with conditions and multiple actions
pub fn create_complex_evening_automation() -> Result<Automation, Box<dyn std::error::Error>> {
    let mut automation = Automation::new(
        Id::new("evening_automation".to_string()),
        "Evening Routine".to_string(),
        "Complex evening automation with multiple conditions and actions".to_string(),
    );

    // Trigger: Time-based (6 PM)
    let time_trigger = TriggerNode::new(
        Id::new("evening_trigger".to_string()),
        "Evening Time Trigger".to_string(),
        "Triggered at 6 PM".to_string(),
        TriggerNodeType::ScheduledTime {
            hour: 18,
            minute: 0,
            days_of_week: vec![1, 2, 3, 4, 5], // Monday to Friday
        },
    );

    // Condition: Check if anyone is home
    let presence_condition = ConditionNode::new(
        Id::new("presence_check".to_string()),
        "Presence Check".to_string(),
        "Check if anyone is home".to_string(),
        ConditionNodeType::EntityState {
            entity_id: Id::new("home_occupancy".to_string()),
            expected_state: "occupied".to_string(),
        },
    );

    // Condition: Check if it's dark outside
    let light_condition = ConditionNode::new(
        Id::new("darkness_check".to_string()),
        "Darkness Check".to_string(),
        "Check if it's dark outside".to_string(),
        ConditionNodeType::NumericComparison {
            entity_id: Id::new("outdoor_light_sensor".to_string()),
            value: 50.0,
            comparison: ThresholdComparison::LessThan,
        },
    );

    // Logic: AND condition for both presence and darkness
    let and_logic = LogicNode::new(
        Id::new("and_conditions".to_string()),
        "AND Logic".to_string(),
        "Both presence and darkness conditions must be true".to_string(),
        LogicNodeType::Switch {
            condition_results: vec![],
            default_next: Some(Id::new("turn_on_lights".to_string())),
        },
    );

    // Action: Turn on lights
    let lights_action = ActionNode::new(
        Id::new("turn_on_lights".to_string()),
        "Turn On Lights".to_string(),
        "Turn on all evening lights".to_string(),
        ActionNodeType::EntityOnOff {
            entity_id: Id::new("evening_lights_group".to_string()),
            turn_on: true,
        },
    );

    // Action: Set warm lighting
    let warm_light_action = ActionNode::new(
        Id::new("set_warm_light".to_string()),
        "Set Warm Lighting".to_string(),
        "Set lights to warm evening color".to_string(),
        ActionNodeType::SetColor {
            entity_id: Id::new("evening_lights_group".to_string()),
            color: Color::new(255, 200, 150), // Warm white
        },
    );

    // Action: Lower brightness
    let dim_action = ActionNode::new(
        Id::new("dim_lights".to_string()),
        "Dim Lights".to_string(),
        "Set lights to comfortable evening brightness".to_string(),
        ActionNodeType::SetBrightness {
            entity_id: Id::new("evening_lights_group".to_string()),
            brightness: 60,
        },
    );

    // Action: Send notification
    let notification_action = ActionNode::new(
        Id::new("evening_notification".to_string()),
        "Evening Notification".to_string(),
        "Send evening routine notification".to_string(),
        ActionNodeType::SendNotification {
            title: "Evening Routine".to_string(),
            message: "Evening lighting has been activated".to_string(),
            priority: NotificationPriority::Normal,
        },
    );

    // Add all nodes to automation
    automation.add_workflow_node(WorkflowNodeData::Trigger(time_trigger))?;
    automation.add_workflow_node(WorkflowNodeData::Condition(presence_condition))?;
    automation.add_workflow_node(WorkflowNodeData::Condition(light_condition))?;
    automation.add_workflow_node(WorkflowNodeData::Logic(and_logic))?;
    automation.add_workflow_node(WorkflowNodeData::Action(lights_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(warm_light_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(dim_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(notification_action))?;

    // Connect nodes to create workflow
    automation.connect_nodes(
        &Id::new("evening_trigger".to_string()),
        &Id::new("presence_check".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("presence_check".to_string()),
        &Id::new("darkness_check".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("darkness_check".to_string()),
        &Id::new("and_conditions".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("and_conditions".to_string()),
        &Id::new("turn_on_lights".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("turn_on_lights".to_string()),
        &Id::new("set_warm_light".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("set_warm_light".to_string()),
        &Id::new("dim_lights".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("dim_lights".to_string()),
        &Id::new("evening_notification".to_string()),
    )?;

    Ok(automation)
}

/// Example: Security automation with parallel actions
pub fn create_security_automation() -> Result<Automation, Box<dyn std::error::Error>> {
    let mut automation = Automation::new(
        Id::new("security_automation".to_string()),
        "Security Alert System".to_string(),
        "Activate security measures when motion is detected while away".to_string(),
    );

    // Trigger: Motion detected
    let motion_trigger = TriggerNode::new(
        Id::new("motion_trigger".to_string()),
        "Motion Detection".to_string(),
        "Motion detected by security sensor".to_string(),
        TriggerNodeType::DeviceEvent {
            device_id: Id::new("security_motion_sensor".to_string()),
            event_type: "motion_detected".to_string(),
        },
    );

    // Condition: Check if home is in away mode
    let away_condition = ConditionNode::new(
        Id::new("away_mode_check".to_string()),
        "Away Mode Check".to_string(),
        "Check if home is in away/vacation mode".to_string(),
        ConditionNodeType::EntityState {
            entity_id: Id::new("home_mode".to_string()),
            expected_state: "away".to_string(),
        },
    );

    // Logic: Parallel execution for multiple security actions
    let parallel_logic = LogicNode::new(
        Id::new("parallel_actions".to_string()),
        "Parallel Security Actions".to_string(),
        "Execute multiple security actions simultaneously".to_string(),
        LogicNodeType::Parallel {
            branch_names: vec![
                "lights".to_string(),
                "camera".to_string(),
                "notification".to_string(),
                "alarm".to_string(),
            ],
        },
    );

    // Action: Turn on all lights
    let lights_action = ActionNode::new(
        Id::new("security_lights".to_string()),
        "Security Lights".to_string(),
        "Turn on all lights for security".to_string(),
        ActionNodeType::EntityOnOff {
            entity_id: Id::new("all_lights_group".to_string()),
            turn_on: true,
        },
    );

    // Action: Start recording cameras
    let camera_action = ActionNode::new(
        Id::new("start_recording".to_string()),
        "Start Camera Recording".to_string(),
        "Start recording on all security cameras".to_string(),
        ActionNodeType::CallService {
            service_name: "camera.start_recording".to_string(),
            parameters: vec![
                ("entity_id".to_string(), "all_cameras".to_string()),
                ("duration".to_string(), "300".to_string()), // 5 minutes
            ],
        },
    );

    // Action: Send critical notification
    let notification_action = ActionNode::new(
        Id::new("security_alert".to_string()),
        "Security Alert".to_string(),
        "Send critical security notification".to_string(),
        ActionNodeType::SendNotification {
            title: "SECURITY ALERT".to_string(),
            message: "Motion detected while away! Cameras recording, lights activated.".to_string(),
            priority: NotificationPriority::Critical,
        },
    );

    // Action: Activate alarm after delay
    let delay_action = ActionNode::new(
        Id::new("alarm_delay".to_string()),
        "Alarm Delay".to_string(),
        "Wait 30 seconds before activating alarm".to_string(),
        ActionNodeType::Delay {
            duration: Duration::from_secs(30),
        },
    );

    let alarm_action = ActionNode::new(
        Id::new("activate_alarm".to_string()),
        "Activate Alarm".to_string(),
        "Activate the security alarm system".to_string(),
        ActionNodeType::CallService {
            service_name: "alarm.trigger".to_string(),
            parameters: vec![
                ("entity_id".to_string(), "security_alarm".to_string()),
                ("type".to_string(), "intrusion".to_string()),
            ],
        },
    );

    // Add all nodes
    automation.add_workflow_node(WorkflowNodeData::Trigger(motion_trigger))?;
    automation.add_workflow_node(WorkflowNodeData::Condition(away_condition))?;
    automation.add_workflow_node(WorkflowNodeData::Logic(parallel_logic))?;
    automation.add_workflow_node(WorkflowNodeData::Action(lights_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(camera_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(notification_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(delay_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(alarm_action))?;

    // Connect workflow
    automation.connect_nodes(
        &Id::new("motion_trigger".to_string()),
        &Id::new("away_mode_check".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("away_mode_check".to_string()),
        &Id::new("parallel_actions".to_string()),
    )?;

    // Parallel branches
    automation.connect_nodes(
        &Id::new("parallel_actions".to_string()),
        &Id::new("security_lights".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("parallel_actions".to_string()),
        &Id::new("start_recording".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("parallel_actions".to_string()),
        &Id::new("security_alert".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("parallel_actions".to_string()),
        &Id::new("alarm_delay".to_string()),
    )?;

    // Alarm delay sequence
    automation.connect_nodes(
        &Id::new("alarm_delay".to_string()),
        &Id::new("activate_alarm".to_string()),
    )?;

    Ok(automation)
}

/// Example: Temperature control automation with loops and thresholds
pub fn create_temperature_control_automation() -> Result<Automation, Box<dyn std::error::Error>> {
    let mut automation = Automation::new(
        Id::new("temperature_control".to_string()),
        "Smart Temperature Control".to_string(),
        "Automatically adjust temperature based on conditions and preferences".to_string(),
    );

    // Trigger: Temperature threshold
    let temp_trigger = TriggerNode::new(
        Id::new("temp_threshold".to_string()),
        "Temperature Threshold".to_string(),
        "Triggered when temperature goes above 25°C".to_string(),
        TriggerNodeType::NumericThreshold {
            entity_id: Id::new("indoor_temperature".to_string()),
            threshold: 25.0,
            comparison: ThresholdComparison::GreaterThan,
        },
    );

    // Condition: Check time (only during day)
    let time_condition = ConditionNode::new(
        Id::new("daytime_check".to_string()),
        "Daytime Check".to_string(),
        "Only activate cooling during daytime hours".to_string(),
        ConditionNodeType::TimeCondition {
            start_hour: 8,
            start_minute: 0,
            end_hour: 22,
            end_minute: 0,
        },
    );

    // Condition: Check if anyone is home
    let occupancy_condition = ConditionNode::new(
        Id::new("occupancy_check".to_string()),
        "Occupancy Check".to_string(),
        "Check if anyone is home".to_string(),
        ConditionNodeType::EntityState {
            entity_id: Id::new("home_occupancy".to_string()),
            expected_state: "occupied".to_string(),
        },
    );

    // Action: Turn on AC
    let ac_action = ActionNode::new(
        Id::new("turn_on_ac".to_string()),
        "Turn On AC".to_string(),
        "Turn on air conditioning".to_string(),
        ActionNodeType::CallService {
            service_name: "climate.turn_on".to_string(),
            parameters: vec![
                ("entity_id".to_string(), "ac_living_room".to_string()),
                ("temperature".to_string(), "23".to_string()),
            ],
        },
    );

    // Action: Close blinds
    let blinds_action = ActionNode::new(
        Id::new("close_blinds".to_string()),
        "Close Blinds".to_string(),
        "Close blinds to block sunlight".to_string(),
        ActionNodeType::CallService {
            service_name: "cover.close_cover".to_string(),
            parameters: vec![
                ("entity_id".to_string(), "living_room_blinds".to_string()),
            ],
        },
    );

    // Action: Send notification
    let notification_action = ActionNode::new(
        Id::new("cooling_notification".to_string()),
        "Cooling Notification".to_string(),
        "Notify about cooling activation".to_string(),
        ActionNodeType::SendNotification {
            title: "Temperature Control".to_string(),
            message: "Cooling system activated due to high temperature".to_string(),
            priority: NotificationPriority::Normal,
        },
    );

    // Add all nodes
    automation.add_workflow_node(WorkflowNodeData::Trigger(temp_trigger))?;
    automation.add_workflow_node(WorkflowNodeData::Condition(time_condition))?;
    automation.add_workflow_node(WorkflowNodeData::Condition(occupancy_condition))?;
    automation.add_workflow_node(WorkflowNodeData::Action(ac_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(blinds_action))?;
    automation.add_workflow_node(WorkflowNodeData::Action(notification_action))?;

    // Connect workflow
    automation.connect_nodes(
        &Id::new("temp_threshold".to_string()),
        &Id::new("daytime_check".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("daytime_check".to_string()),
        &Id::new("occupancy_check".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("occupancy_check".to_string()),
        &Id::new("turn_on_ac".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("turn_on_ac".to_string()),
        &Id::new("close_blinds".to_string()),
    )?;

    automation.connect_nodes(
        &Id::new("close_blinds".to_string()),
        &Id::new("cooling_notification".to_string()),
    )?;

    Ok(automation)
}

/// Demonstration function showing how to use the automation examples
pub fn demonstrate_dag_automations() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== DAG Automation Examples ===\n");

    // Simple automation
    let mut simple_auto = create_simple_button_automation()?;
    println!("1. Simple Button Automation:");
    println!("   {}", simple_auto.get_workflow_stats());
    println!("   Validation: {:?}", simple_auto.validate_workflow());
    println!("   Execution order: {:?}\n", simple_auto.get_execution_order());

    // Complex automation
    let mut complex_auto = create_complex_evening_automation()?;
    println!("2. Complex Evening Automation:");
    println!("   {}", complex_auto.get_workflow_stats());
    println!("   Validation: {:?}", complex_auto.validate_workflow());
    let execution_results = complex_auto.execute_workflow();
    println!("   Execution results: {:?}\n", execution_results);

    // Security automation
    let mut security_auto = create_security_automation()?;
    println!("3. Security Automation:");
    println!("   {}", security_auto.get_workflow_stats());
    println!("   Validation: {:?}", security_auto.validate_workflow());
    println!("   Trigger nodes: {:?}\n", security_auto.get_trigger_node_ids());

    // Temperature control automation
    let mut temp_auto = create_temperature_control_automation()?;
    println!("4. Temperature Control Automation:");
    println!("   {}", temp_auto.get_workflow_stats());
    println!("   Validation: {:?}", temp_auto.validate_workflow());
    println!("   Execution order: {:?}\n", temp_auto.get_execution_order());

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simple_automation_creation() {
        let automation = create_simple_button_automation().unwrap();
        assert_eq!(automation.get_workflow_stats().total_nodes, 2);
        assert_eq!(automation.get_workflow_stats().total_edges, 1);
        assert!(automation.validate_workflow().is_ok());
    }

    #[test]
    fn test_complex_automation_creation() {
        let automation = create_complex_evening_automation().unwrap();
        assert_eq!(automation.get_workflow_stats().total_nodes, 8);
        assert_eq!(automation.get_workflow_stats().trigger_count, 1);
        assert!(automation.validate_workflow().is_ok());
    }

    #[test]
    fn test_security_automation_creation() {
        let automation = create_security_automation().unwrap();
        assert!(automation.get_workflow_stats().total_nodes > 5);
        assert_eq!(automation.get_workflow_stats().trigger_count, 1);
        assert!(automation.validate_workflow().is_ok());
    }

    #[test]
    fn test_temperature_automation_creation() {
        let automation = create_temperature_control_automation().unwrap();
        assert_eq!(automation.get_workflow_stats().total_nodes, 6);
        assert_eq!(automation.get_workflow_stats().trigger_count, 1);
        assert!(automation.validate_workflow().is_ok());
    }
} 