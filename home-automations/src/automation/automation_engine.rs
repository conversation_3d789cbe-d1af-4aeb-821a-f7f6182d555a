use std::sync::Arc;

use crate::{
    automation::Automation,
    event_bus::{EventBus, events::DeviceEvent},
};

use home_automations::Result;

#[derive(Debug)]
pub struct AutomationEngine {
    automations: Vec<Automation>,
    event_bus: Arc<EventBus>,
}

impl AutomationEngine {
    pub fn new(event_bus: Arc<EventBus>) -> Self {
        Self {
            automations: Vec::new(),
            event_bus,
        }
    }

    pub async fn start(&mut self) {
        let mut device_event_receiver = self.event_bus.subscribe_to_device_events().await;

        while let Ok(event) = device_event_receiver.recv().await {
            // TODO: handle errors
            self.handle_device_events(event).await;
        }
    }

    pub fn add_automation(&mut self, automation: Automation) -> &mut Self {
        self.automations.push(automation);

        self
    }

    pub fn remove_automation(&mut self, automation: Automation) -> &mut Self {
        self.automations.retain(|a| a.name != automation.name);

        self
    }

    async fn handle_device_events(&mut self, event: DeviceEvent) -> Result<()> {
        for automation in &mut self.automations.iter_mut() {
            if automation.can_trigger_from_event(&event) {
                automation.execute_from_event(&event);
            }
        }

        Ok(())
    }
}
